<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接测试 - 修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <h1>🔧 WebSocket 连接修复验证</h1>
    
    <div class="status info">
        <strong>修复内容：</strong>
        <br>• ✅ 前端用户ID生成已修复（使用稳定ID）
        <br>• ✅ 前端开发服务器已重启
        <br>• ✅ WebSocket代理配置正常
        <br>• 🧪 现在测试连接稳定性
    </div>
    
    <div id="status" class="status warning">
        状态：准备测试
    </div>
    
    <div>
        <button onclick="testStableConnection()">测试稳定连接</button>
        <button onclick="testMultipleConnections()">测试多次连接</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="log" class="log"></div>

    <script>
        let ws = null;
        let connectionCount = 0;
        let isTestingMultiple = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `状态：${message}`;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testStableConnection() {
            log('🧪 开始测试稳定连接...');
            
            // 使用修复后的稳定用户ID
            const stableUserId = 'stable_test_user_123';
            const sessionId = 'stable_test_session_456';
            
            // 通过前端代理连接
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}/ws/${stableUserId}?conversation_id=${sessionId}`;
            
            log(`📡 连接URL: ${wsUrl}`);
            log(`👤 用户ID: ${stableUserId} (稳定)`);
            log(`💬 会话ID: ${sessionId} (稳定)`);
            
            updateStatus('正在连接...', 'warning');
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    log('✅ WebSocket连接成功建立！');
                    updateStatus('已连接', 'success');
                    
                    // 发送测试消息
                    const testMessage = {
                        type: 'text',
                        content: '这是一个测试消息',
                        user_id: stableUserId,
                        session_id: sessionId
                    };
                    
                    ws.send(JSON.stringify(testMessage));
                    log('📤 发送测试消息');
                };
                
                ws.onmessage = function(event) {
                    log(`📥 收到消息: ${event.data}`);
                };
                
                ws.onclose = function(event) {
                    log(`❌ 连接关闭: 代码=${event.code}, 原因=${event.reason}`);
                    updateStatus('连接已关闭', 'error');
                };
                
                ws.onerror = function(error) {
                    log(`💥 连接错误: ${error}`);
                    updateStatus('连接错误', 'error');
                };
                
            } catch (error) {
                log(`💥 创建连接失败: ${error}`);
                updateStatus('连接失败', 'error');
            }
        }

        function testMultipleConnections() {
            if (isTestingMultiple) {
                log('⏹️ 停止多次连接测试');
                isTestingMultiple = false;
                return;
            }
            
            log('🔄 开始测试多次连接（验证稳定性）...');
            isTestingMultiple = true;
            connectionCount = 0;
            
            const button = event.target;
            button.textContent = '停止测试';
            
            function connectOnce() {
                if (!isTestingMultiple) {
                    button.textContent = '测试多次连接';
                    return;
                }
                
                connectionCount++;
                const stableUserId = 'stable_test_user_123'; // 使用相同的稳定ID
                const sessionId = `stable_test_session_${connectionCount}`;
                
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const host = window.location.host;
                const wsUrl = `${protocol}//${host}/ws/${stableUserId}?conversation_id=${sessionId}`;
                
                log(`🔄 第${connectionCount}次连接测试 - 用户ID: ${stableUserId}`);
                
                const testWs = new WebSocket(wsUrl);
                
                testWs.onopen = function() {
                    log(`✅ 第${connectionCount}次连接成功`);
                    
                    // 2秒后关闭连接
                    setTimeout(() => {
                        testWs.close();
                        
                        // 1秒后进行下一次连接
                        setTimeout(connectOnce, 1000);
                    }, 2000);
                };
                
                testWs.onclose = function(event) {
                    log(`📴 第${connectionCount}次连接关闭`);
                };
                
                testWs.onerror = function(error) {
                    log(`💥 第${connectionCount}次连接错误`);
                    setTimeout(connectOnce, 2000); // 错误后重试
                };
            }
            
            connectOnce();
        }

        // 页面加载时显示修复信息
        window.onload = function() {
            log('🎯 WebSocket连接修复验证工具已加载');
            log('📋 修复内容：');
            log('   • 使用useMemo确保用户ID稳定');
            log('   • 修复WebSocket URL构建逻辑');
            log('   • 更新useEffect依赖项');
            log('   • 前端开发服务器已重启');
            log('');
            log('🧪 点击"测试稳定连接"开始验证修复效果');
        };
    </script>
</body>
</html>
