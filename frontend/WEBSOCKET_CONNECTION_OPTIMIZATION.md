# WebSocket 连接优化 - 添加等待机制

## 🎯 问题描述

用户建议在发送WebSocket请求前等待2秒，让WebSocket服务连接建立成功。这是一个很好的建议，因为：

1. WebSocket连接建立需要时间
2. 在连接未完全建立时发送消息会失败
3. 用户体验会受到影响

## 🔧 优化方案

### 1. 添加连接等待机制

在 `ChatWindow.tsx` 中添加了 `waitForConnection` 函数：

```typescript
const waitForConnection = useCallback(async (maxWaitTime = 5000): Promise<boolean> => {
  if (!webSocketService.current) {
    return false
  }

  if (webSocketService.current.isConnected()) {
    return true
  }

  console.log('等待WebSocket连接建立...')
  message.info('正在建立连接，请稍候...')

  return new Promise((resolve) => {
    const startTime = Date.now()
    const checkInterval = setInterval(() => {
      if (webSocketService.current?.isConnected()) {
        clearInterval(checkInterval)
        console.log('WebSocket连接已建立')
        resolve(true)
      } else if (Date.now() - startTime > maxWaitTime) {
        clearInterval(checkInterval)
        console.log('WebSocket连接等待超时')
        message.error('连接超时，请检查网络连接')
        resolve(false)
      }
    }, 100) // 每100ms检查一次
  })
}, [])
```

### 2. 优化发送消息逻辑

更新了 `handleSendMessage` 函数：

```typescript
const handleSendMessage = useCallback(async () => {
  if (!inputValue.trim()) {
    return
  }

  const messageContent = inputValue.trim()
  setInputValue('')
  setIsLoading(true)

  try {
    // 等待连接建立（最多等待5秒）
    const isConnected = await waitForConnection(5000)
    
    if (!isConnected) {
      setIsLoading(false)
      setInputValue(messageContent) // 恢复输入内容
      return
    }

    // 连接建立后再发送消息
    // ... 发送逻辑
  } catch (error) {
    // 错误处理，恢复输入内容
    setInputValue(messageContent)
    setIsLoading(false)
  }
}, [inputValue, waitForConnection])
```

### 3. 添加连接超时机制

在 `WebSocketService.ts` 中添加了连接超时：

```typescript
async connect(): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      this.setConnectionStatus('connecting');
      
      // 设置连接超时（10秒）
      const connectionTimeout = setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          this.ws.close();
          this.setConnectionStatus('error');
          reject(new Error('WebSocket连接超时'));
        }
      }, 10000);
      
      // ... WebSocket连接逻辑
      
      this.ws.onopen = () => {
        clearTimeout(connectionTimeout);
        console.log('✅ WebSocket连接已成功建立');
        // ... 其他逻辑
      };
      
      this.ws.onerror = (error) => {
        clearTimeout(connectionTimeout);
        // ... 错误处理
      };
    }
  });
}
```

## 🎯 优化效果

### 用户体验改进
1. **智能等待**：自动等待连接建立，无需用户手动等待
2. **友好提示**：显示"正在建立连接，请稍候..."消息
3. **错误恢复**：连接失败时恢复用户输入的内容
4. **超时保护**：避免无限等待，5秒后超时

### 技术改进
1. **连接状态检测**：每100ms检查一次连接状态
2. **超时机制**：WebSocket连接10秒超时，消息发送5秒超时
3. **资源清理**：正确清理定时器，避免内存泄漏
4. **错误处理**：完善的错误处理和用户反馈

## 📋 工作流程

### 发送消息时的流程：
1. 用户点击发送或按回车
2. 检查输入内容是否为空
3. 清空输入框，显示加载状态
4. **等待WebSocket连接建立**（最多5秒）
5. 连接建立后发送消息
6. 如果连接失败，恢复输入内容并提示错误

### 连接建立时的流程：
1. 开始连接，状态设为"connecting"
2. 设置10秒连接超时
3. WebSocket连接建立成功
4. 清除超时定时器
5. 状态设为"connected"
6. 开始心跳和消息队列处理

## 🧪 测试建议

1. **正常连接测试**：验证连接建立和消息发送
2. **网络延迟测试**：模拟慢网络环境
3. **连接失败测试**：断开后端服务，测试错误处理
4. **超时测试**：验证超时机制是否正常工作

## 📈 性能考虑

- **检查频率**：每100ms检查连接状态，平衡响应性和性能
- **超时设置**：连接超时10秒，消息发送超时5秒，适合大多数网络环境
- **内存管理**：正确清理定时器，避免内存泄漏
- **用户反馈**：及时的状态提示，提升用户体验

这个优化解决了你提到的连接等待问题，现在系统会智能地等待连接建立后再发送消息！
